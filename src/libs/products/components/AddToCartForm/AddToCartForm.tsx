import { FormEventHandler, useCallback, useState } from 'react';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { AddToCartButton } from './components/AddToCartButton/AddToCartButton';
import {
  AddToCartInput,
  type AddToCartInputProps,
} from '../AddToCartInput/AddToCartInput';

interface AddToCartFormProps {
  productOfferId: string;
  increments: number;
}
export const AddToCartForm = ({
  productOfferId,
  increments,
}: AddToCartFormProps) => {
  const { addToCart, offersMapData } = useCartStore();
  const [amount, setAmount] = useState(increments ?? 1);
  const { isLoading = false, quantity = 0 } =
    offersMapData[productOfferId] ?? {};

  const handleQuantityUpdate: AddToCartInputProps['onUpdate'] = ({
    amount: newAmount,
  }) => {
    setAmount(newAmount);
  };

  const handleAddToCartClick: FormEventHandler<HTMLFormElement> = useCallback(
    (event) => {
      event.preventDefault();

      addToCart({
        offers: [
          {
            productOfferId,
            quantity: quantity + amount,
          },
        ],
        // TODO: Handle error better
        onError: () => {},
      });
    },
    [productOfferId, addToCart, quantity, amount],
  );

  return (
    <form onSubmit={handleAddToCartClick}>
      <Flex gap="12px" flex="grow">
        <AddToCartInput
          originalAmount={increments}
          minIncrement={increments}
          onUpdate={handleQuantityUpdate}
        />
        <AddToCartButton
          quantityInCart={quantity}
          isLoading={isLoading}
          fullWidth
          isDisabled={amount === 0}
        />
      </Flex>
    </form>
  );
};
