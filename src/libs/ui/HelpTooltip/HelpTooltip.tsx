import { Tooltip } from '@/libs/ui/Tooltip';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';

interface HelpTooltipProps {
  color?: string;
  message: string;
}

export const HelpTooltip = ({
  color = '#25B7D3',
  message,
}: HelpTooltipProps) => (
  <Tooltip label={message} side="right" align="start">
    <Button variant="unstyled" type="button">
      <Icon name="question" color={color} size="0.8rem" />
    </Button>
  </Tooltip>
);
