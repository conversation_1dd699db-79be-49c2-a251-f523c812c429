import { Button } from '@/libs/ui/Button/Button';
import styles from './Tabs.module.css';
interface TabsProps {
  active: number;
  tabs: {
    label: string;
    onClick: (index: number) => void;
  }[];
}
export const Tabs = ({ tabs, active }: TabsProps) => {
  return (
    <div className="flex rounded-lg bg-[#f1f2f4]">
      {tabs.map(({ label, onClick }, index) => (
        <div className="flex flex-1" key={label}>
          <Button
            variant="unstyled"
            onClick={() => onClick(index)}
            className={active === index ? styles.activeTab : ''}
            style={{
              paddingInline: '1rem',
              width: '100%',
              height: '40px',
              textAlign: 'center',
              borderRadius: '0.5rem',
              whiteSpace: 'nowrap',
            }}
          >
            {label}
          </Button>
        </div>
      ))}
    </div>
  );
};
