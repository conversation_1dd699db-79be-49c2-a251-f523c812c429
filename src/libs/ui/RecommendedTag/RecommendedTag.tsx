import { ReactNode } from 'react';
import { mergeClasses } from '@/utils';
import { cva, type VariantProps } from 'class-variance-authority';

const recommendedTagVariants = cva(
  'absolute z-[1] text-white font-medium text-xs uppercase whitespace-nowrap bg-[#3646ac]',
  {
    variants: {
      size: {
        xs: 'pl-3 pr-2 py-0.5',
        sm: 'px-1.5 py-1',
        md: 'pl-3 pr-2 py-2.5',
        lg: 'px-4 py-2 text-sm rounded-r-lg',
      },
      placement: {
        top: 'rounded-bl rounded-br',
        bottom: 'rounded-tl rounded-tr',
        left: 'rounded-tr rounded-br',
        right: 'rounded-tl rounded-bl',
        'top-left': 'rounded-tr rounded-bl',
        'top-right': 'rounded-tl rounded-br',
        'bottom-left': 'rounded-tr rounded-bl',
        'bottom-right': 'rounded-tl rounded-br',
      },
    },
    defaultVariants: {
      size: 'md',
      placement: 'left',
    },
  },
);

export type RecommendedTagProps = VariantProps<
  typeof recommendedTagVariants
> & {
  top?: string;
  left?: string;
  children: ReactNode;
  className?: string;
};

export const RecommendedTag = ({
  top = '0',
  left = '0',
  size = 'md',
  placement = 'left',
  children,
  className,
}: RecommendedTagProps) => {
  return (
    <span
      style={{
        top,
        left,
      }}
      className={mergeClasses(
        recommendedTagVariants({ size, placement }),
        className,
      )}
    >
      {children}
    </span>
  );
};
