import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { PurchaseHistory } from '@/libs/products/components/PurchaseHistory/PurchaseHistory';
import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import type { CartItemType } from '@/libs/cart/types';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { useBoxLoading } from '@/apps/shop/stores/useBoxLoadingStore';
import { getMeasure } from '@/apps/shop/pages/OrderHistory/utils/getMeasure';
import { Icon } from '@/libs/icons/Icon';

export const CartVendorProductInfo = ({ data }: { data: CartItemType }) => {
  const { id, product, productOfferId } = data;
  const offer = product.offers.find(({ id }) => productOfferId === id);

  const { swapOfferCartItem } = useCartStore();
  const { toggleBoxLoading } = useBoxLoading();

  const { apiRequest: swapVendor } = useAsyncRequest({
    apiFunc: async (productOfferId) =>
      swapOfferCartItem(id, productOfferId as string),
  });

  const handleSelectSwapVendor = (value: string | null) => {
    toggleBoxLoading('#cart-layout', () => swapVendor(value));
  };

  if (!offer) {
    return null;
  }

  const productUrl = getProductUrl(product.id, productOfferId);
  const measure = getMeasure({ item: { productOfferId, product } });

  return (
    <div className="flex flex-col">
      <Link
        to={productUrl}
        className="text-[#333] no-underline hover:underline"
      >
        <h3 className="max-w-9/10 text-sm leading-5 font-semibold text-black">
          {product.name}
        </h3>
      </Link>
      <div className="mt-1 mb-4 flex items-center">
        <span className="text-xs text-gray-500">
          SKU:
          <span className="ml-0.5 font-medium text-[#344054]">
            {offer.vendorSku}
          </span>
        </span>
        {!!product.manufacturer && (
          <>
            <div className="divider-v mx-4" />
            <span className="text-xs text-[#344054]">
              {product.manufacturer}
            </span>
          </>
        )}
        {!!measure && (
          <>
            <div className="divider-v mx-4" />
            <span className="text-xs text-gray-500">
              UOM:
              <span className="ml-0.5 font-medium text-[#344054]">
                {measure}
              </span>
            </span>
          </>
        )}
      </div>
      <div className="flex max-w-[260px] flex-col gap-4">
        <VendorSwap
          offers={product.offers}
          currentOfferId={productOfferId}
          onSwap={handleSelectSwapVendor}
        />

        <div className="w-full">
          <PurchaseHistory
            productId={offer.id}
            lastOrderedAt={offer.lastOrderedAt}
            lastOrderedQuantity={offer.lastOrderedQuantity}
          />
        </div>
      </div>
      {product.isControlled222Form && (
        <div className="flex gap-1.5">
          <Icon name="warning" color="#EFFF63" />
          <p className="text-xs">
            <span className="font-medium">Attention:</span> Product processing
            requires a submitted{' '}
            <span className="font-semibold">DEA Form 222</span>.
          </p>
        </div>
      )}
    </div>
  );
};
