import { useTranslation } from 'react-i18next';
import { PayablesTable } from '@monite/sdk-react';
import { useMoniteToken } from '@/libs/monite/hooks/useMoniteToken';
import { Button } from '@/components/atoms/Button/Button';
import { Loader } from '@/libs/ui/Loader/Loader';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { MoniteProvider } from '@/libs/monite/components/MoniteProvider/MoniteProvider';

export const Invoices = () => {
  const { t } = useTranslation();
  const activeClinic = useAccountStore((state) => state.activeClinic);

  const { token, isLoading, hasError, handleFetchToken, fetchToken, error } =
    useMoniteToken({
      clinicId: activeClinic?.id || '',
    });

  const handleRetry = () => {
    handleFetchToken();
  };

  // Show message if no clinic is selected
  if (!activeClinic?.id) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="max-w-md text-center">
          <h3 className="mb-2 text-lg font-semibold text-gray-600">
            {t('client.invoices.noClinic.title')}
          </h3>
          <p className="mb-4 text-gray-600">
            {t('client.invoices.noClinic.message')}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <Loader size="3rem" />
          <p className="mt-4 text-gray-600">{t('client.invoices.loading')}</p>
        </div>
      </div>
    );
  }

  if (hasError || error) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="max-w-md text-center">
          <h3 className="mb-2 text-lg font-semibold text-red-600">
            {t('client.invoices.error.title')}
          </h3>
          <p className="mb-4 text-gray-600">
            {error || t('client.invoices.error.message')}
          </p>
          <Button onClick={handleRetry} variant="primary">
            {t('client.invoices.error.retry')}
          </Button>
        </div>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <Button onClick={handleRetry} variant="primary">
            {t('client.invoices.load')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('client.invoices.title')}
        </h1>
        <p className="mt-2 text-gray-600">{t('client.invoices.description')}</p>
      </div>

      <MoniteProvider
        token={token}
        fetchToken={fetchToken}
        clinicId={activeClinic.id}
      >
        <PayablesTable />
      </MoniteProvider>
    </div>
  );
};
