import type {
  OrderHistoryDetailItemType,
  OrderHistoryPromotion,
} from '@/libs/orders/types';
import { PROMO_TYPE } from '@/constants';
import { Dollar } from '@/libs/products/components/Dollar/Dollar';
import { OrderHistoryRegularItem } from '../OrderHistoryRegularItem/OrderHistoryRegularItem';

export const OrderHistoryBuyGetPromotionItem = ({
  promotion,
  items,
}: {
  promotion: OrderHistoryPromotion;
  items: OrderHistoryDetailItemType[];
}) => {
  if (!promotion || items.length === 0) return null;

  return (
    <div className="rounded-sm border border-gray-200/70 bg-gray-50">
      <div className="flex gap-3 rounded-t-sm bg-gray-100 p-4">
        <Dollar
          toolTipLabel={`Promotion Type: ${PROMO_TYPE[promotion.type]}`}
        />
        <span className="text-sm font-medium">
          Promotion <span className="mx-1 text-gray-400/80">•</span>{' '}
          <span className="text-xs">{PROMO_TYPE[promotion.type]}</span>:{' '}
          <span className="text-xs text-gray-500">{promotion.name}</span>
        </span>
      </div>

      <div className="grid gap-4 divide-y divide-gray-200/80 p-4 pt-6">
        {items.map((item) => (
          <OrderHistoryRegularItem key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};
