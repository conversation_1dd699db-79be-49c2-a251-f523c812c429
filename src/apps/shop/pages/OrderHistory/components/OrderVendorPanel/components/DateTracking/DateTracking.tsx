import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import React from 'react';

export const DateTracking = () => {
  return (
    <div>
      {dateDelivered ? (
        <p className="text-[10px] text-[#666]">
          {dayjs(dateDelivered).format('MMMM D, YYYY')}
        </p>
      ) : (
        etaDate && (
          <p className="flex gap-1 text-[10px] whitespace-nowrap text-[#666]">
            Expected Delivery:
            <span className="font-bold text-[#333]">
              {dayjs(etaDate).format('MMMM D, YYYY')}
            </span>
          </p>
        )
      )}
      {item.product.isControlled222Form && (
        <HelpTooltip
          message="Typical delivery is 5–7 business days after the vendor receives the 222 form."
          color="grey"
        />
      )}
    </div>
  );
};
