import { SortOrderOptionType } from '@/types/utility';
import { PromoType } from './common';

export type StockStatusType =
  | 'IN_STOCK'
  | 'OUT_OF_STOCK'
  | 'DROP_SHIP'
  | 'SPECIAL_ORDER'
  | 'BACKORDER';

export interface OfferType {
  id: string;
  vendor: {
    id: string;
    name: string;
    imageUrl: string;
    type: 'manufacturer' | 'distributor';
  };
  name: string;
  vendorSku: string;
  price: null | string;
  clinicPrice: null | string;
  stockStatus: StockStatusType;
  lastOrderedAt: null | string;
  lastOrderedQuantity: null | number;
  increments: number;
  isRecommended: boolean;
  rebatePercent: null | string;
  product: ProductType;
  unitOfMeasure: string | null;
  size: number | null;
}

export interface ProductType {
  id: string;
  name: string;
  imageUrl: null | string;
  isFavorite: boolean;
  manufacturer: null | string;
  manufacturerSku: null | string;
  description: null | string;
  attributes: {
    name: string;
    value: string;
  }[];
  offers: OfferType[];
  isHazardous: boolean;
  requiresPrescription: boolean;
  requiresColdShipping: boolean;
  isControlledSubstance: boolean;
  isControlled222Form: boolean;
  requiresPedigree: boolean;
  promotions?: PromoType[];
}

export type CatalogSyncStatusType =
  | 'pending'
  | 'running'
  | 'succeeded'
  | 'failed';

export type IntegrationPointType =
  | 'sync_product_catalog'
  | 'place_orders'
  | 'sync_order_status'
  | 'sync_order_shipments'
  | 'sync_order_invoices'
  | 'reconcile_order_lines';

export interface VendorType {
  id: string;
  name: string;
  status: 'disconnected' | 'connecting' | 'connected';
  type: 'manufacturer' | 'distributor';
  imageUrl: string;
  alert: {
    id: string;
    integrationConnectionId: string;
    type: 'info' | 'error';
    message: string;
    createdAt: string;
    updatedAt: string;
  } | null;
  integrationPoints: IntegrationPointType[];
  authenticationKind: string;
  authenticationConfiguration: null | {
    authorizationUri: string;
  };
  lastProductCatalogSync: null | {
    id: string;
    status: CatalogSyncStatusType;
    createdAt: string;
    updatedAt: string;
  };
}

export interface SearchParamsProps<T> {
  query: string;
  sortBy?: keyof T;
  sortOrder?: SortOrderOptionType;
  page: number;
  perPage: string;
  clinicId?: string;
  vendorIds?: string;
}

export interface SavedItemType {
  id: string;
  productOffer: OfferType;
  quantity: number;
  createdAt: string;
  updatedAt: string;
}
